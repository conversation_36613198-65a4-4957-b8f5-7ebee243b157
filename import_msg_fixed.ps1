# ======================
# MSG File Import Script - Fixed Version
# ======================
# This script imports MSG files into Outlook with folder structure reconstruction

# ======================
# CONFIGURATION
# ======================
$batchSize = 50
$waitSeconds = 3
$logFile = "failed_imports.txt"

# ======================
# FUNCTION: Detect Classic Outlook Installation
# ======================
function Get-ClassicOutlookPath {
    Write-Host "🔍 Detecting Classic Outlook installation..." -ForegroundColor Cyan
    
    $officeVersions = @("16.0", "15.0", "14.0", "12.0")
    $regPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Office",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Office"
    )
    
    $classicOutlookPaths = @()
    
    foreach ($regPath in $regPaths) {
        foreach ($ver in $officeVersions) {
            $fullPath = Join-Path $regPath "$ver\Outlook\InstallRoot"
            try {
                if (Test-Path $fullPath) {
                    $path = Get-ItemPropertyValue -Path $fullPath -Name "Path" -ErrorAction Stop
                    $exe = Join-Path $path "OUTLOOK.EXE"
                    if (Test-Path $exe) {
                        $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($exe)
                        
                        if ($version.ProductName -like "*Microsoft Outlook*" -and 
                            $version.ProductName -notlike "*New*") {
                            $classicOutlookPaths += @{
                                Path = $exe
                                Version = $version.FileVersion
                                ProductName = $version.ProductName
                            }
                            Write-Host "✅ Found Classic Outlook: $($version.ProductName) v$($version.FileVersion)" -ForegroundColor Green
                        }
                    }
                }
            } catch {
                # Silently continue if registry key doesn't exist
            }
        }
    }
    
    if ($classicOutlookPaths.Count -eq 0) {
        Write-Warning "❌ No Classic Outlook installation detected."
        Write-Host "💡 Make sure Classic Outlook (not New Outlook) is installed." -ForegroundColor Yellow
        return $null
    }
    
    # Return the newest version found
    $newestOutlook = $classicOutlookPaths | Sort-Object Version -Descending | Select-Object -First 1
    Write-Host "🎯 Using Classic Outlook: $($newestOutlook.ProductName)" -ForegroundColor Green
    return $newestOutlook.Path
}

# ======================
# FUNCTION: Register Outlook COM if needed
# ======================
function Register-OutlookCom {
    $outlookPath = Get-ClassicOutlookPath
    
    if (-not $outlookPath) {
        return $false
    }

    Write-Host "🔄 Attempting to re-register COM..." -ForegroundColor Yellow

    try {
        Start-Process -FilePath $outlookPath -ArgumentList "/regserver" -Wait -NoNewWindow -WindowStyle Hidden
        Write-Host "✅ Successfully re-registered Outlook COM." -ForegroundColor Green
        Start-Sleep -Seconds 2
        return $true
    } catch {
        Write-Error "❌ Failed to re-register COM: $($_.Exception.Message)"
        return $false
    }
}

# ======================
# FUNCTION: Get or Create Folder
# ======================
function Get-OrCreate-OutlookFolder {
    param (
        [Microsoft.Office.Interop.Outlook.MAPIFolder]$parentFolder,
        [string]$subFolderName
    )
    try {
        return $parentFolder.Folders.Item($subFolderName)
    } catch {
        return $parentFolder.Folders.Add($subFolderName)
    }
}

# ======================
# FUNCTION: Extract Folder Path from MSG Properties
# ======================
function Get-FolderFromMetadata {
    param ($mail)
    
    try {
        # Method 1: Check message class and other indicators
        $messageClass = $mail.MessageClass
        $isRead = $mail.UnRead -eq $false
        $isSent = $mail.Sent
        $sentOn = $mail.SentOn
        $receivedTime = $mail.ReceivedTime
        
        # Method 2: Determine folder based on message characteristics
        if ($messageClass -like "IPM.Note*") {
            # Regular email message
            if ($isSent -and $null -ne $sentOn -and $sentOn -ne [datetime]::MinValue) {
                return "Sent Items"
            } elseif ($mail.Sent -eq $false -and ($null -eq $receivedTime -or $receivedTime -eq [datetime]::MinValue)) {
                return "Drafts"
            } elseif ($null -ne $receivedTime -and $receivedTime -ne [datetime]::MinValue) {
                return "Inbox"
            }
        } elseif ($messageClass -like "IPM.Schedule*") {
            return "Calendar"
        } elseif ($messageClass -like "IPM.Contact*") {
            return "Contacts"
        } elseif ($messageClass -like "IPM.Task*") {
            return "Tasks"
        } elseif ($messageClass -like "IPM.Note.Rules*") {
            return "Deleted Items"
        } elseif ($messageClass -like "IPM.Note.SMIME*") {
            # Encrypted/signed messages
            if ($isSent) {
                return "Sent Items"
            } else {
                return "Inbox"
            }
        }
        
        # Method 3: Check subject line for clues
        $subject = $mail.Subject
        if ($subject) {
            if ($subject -like "*[SPAM]*" -or $subject -like "*[JUNK]*") {
                return "Junk Email"
            }
        }
        
        # Default to Unclassified if we can't determine the folder
        return "Unclassified"
        
    } catch {
        Write-Verbose "Error determining folder for message: $($_.Exception.Message)"
        return "Unclassified"
    }
}

# ======================
# FUNCTION: Test Outlook COM Connection
# ======================
function Test-OutlookConnection {
    try {
        Write-Host "🔄 Testing Outlook COM connection..." -ForegroundColor Cyan
        
        $outlookPath = Get-ClassicOutlookPath
        if (-not $outlookPath) {
            return $false
        }
        
        $outlook = New-Object -ComObject Outlook.Application -ErrorAction Stop
        $namespace = $outlook.GetNamespace("MAPI")
        
        $defaultStore = $namespace.DefaultStore
        if ($defaultStore) {
            Write-Host "✅ Outlook COM connection successful. Default store: $($defaultStore.DisplayName)" -ForegroundColor Green
            return @{
                Success = $true
                Outlook = $outlook
                Namespace = $namespace
            }
        } else {
            Write-Warning "⚠️ Outlook COM connected but no default store found."
            return $false
        }
    } catch {
        Write-Warning "❌ Outlook COM connection failed: $($_.Exception.Message)"
        return $false
    }
}

Write-Host "=== MSG File Import Tool ===" -ForegroundColor Cyan
Write-Host "This tool will import MSG files into Outlook with folder structure reconstruction." -ForegroundColor Yellow
Write-Host ""

# ======================
# INPUT: Source Folder
# ======================
$sourceFolder = Read-Host "Enter full path to the folder containing .msg files"
if (!(Test-Path $sourceFolder)) {
    Write-Error "❌ The folder path '$sourceFolder' is invalid. Exiting."
    exit
}

# Count MSG files
$msgFiles = Get-ChildItem -Path $sourceFolder -Filter *.msg -Recurse
if ($msgFiles.Count -eq 0) {
    Write-Error "❌ No .msg files found in '$sourceFolder'. Exiting."
    exit
}

Write-Host "📧 Found $($msgFiles.Count) MSG files to import." -ForegroundColor Green

# ======================
# INPUT: Outlook Root Folder
# ======================
$targetFolderName = Read-Host "Enter the Outlook root-level folder name to import into (e.g., Testing)"
if ([string]::IsNullOrWhiteSpace($targetFolderName)) {
    Write-Error "❌ Root folder name cannot be empty. Exiting."
    exit
}

# ======================
# Ensure Outlook COM works
# ======================
Write-Host "`n🔧 Initializing Outlook connection..." -ForegroundColor Cyan

try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook -ErrorAction Stop
    Write-Host "✅ Outlook Interop Assembly loaded." -ForegroundColor Green
} catch {
    Write-Error "❌ Failed to load Outlook Interop Assembly. Please ensure Office is properly installed."
    exit
}

$connectionResult = Test-OutlookConnection
if ($connectionResult -eq $false) {
    Write-Warning "❌ Initial Outlook COM connection failed. Attempting to re-register..."
    
    if (-not (Register-OutlookCom)) {
        Write-Error "❌ Unable to register Outlook COM. Please ensure:"
        Write-Host "   1. Classic Outlook (not New Outlook) is installed" -ForegroundColor Yellow
        Write-Host "   2. Outlook has been run at least once and configured" -ForegroundColor Yellow
        Write-Host "   3. Run this script as Administrator if needed" -ForegroundColor Yellow
        exit
    }
    
    Start-Sleep -Seconds 3
    $connectionResult = Test-OutlookConnection
    
    if ($connectionResult -eq $false) {
        Write-Error "❌ Still cannot initialize Outlook COM after re-registration. Exiting."
        exit
    }
}

$outlook = $connectionResult.Outlook
$namespace = $connectionResult.Namespace

# ======================
# INIT OUTLOOK FOLDERS
# ======================
Write-Host "`n📁 Setting up Outlook folder structure..." -ForegroundColor Cyan

try {
    $defaultStore = $namespace.DefaultStore
    $rootFolders = $defaultStore.GetRootFolder().Folders

    $targetRootFolder = $null
    try {
        foreach ($folder in $rootFolders) {
            if ($folder.Name -eq $targetFolderName) {
                $targetRootFolder = $folder
                break
            }
        }
    } catch { }

    if (-not $targetRootFolder) {
        $targetRootFolder = $rootFolders.Add($targetFolderName)
        Write-Host "✅ Created Outlook root folder: '$targetFolderName'" -ForegroundColor Green
    } else {
        Write-Host "✅ Using existing Outlook folder: '$targetFolderName'" -ForegroundColor Green
    }

} catch {
    Write-Error "❌ Failed to access or create Outlook folders: $($_.Exception.Message)"
    exit
}

# ======================
# PREPARE IMPORT TRACKING
# ======================
$totalFiles = $msgFiles.Count
$processedCount = 0
$failedCount = 0
$successCount = 0
$failedList = @()
$folderStats = @{}

$failedFolderPath = Join-Path $sourceFolder "failed_imported_mail"
if (!(Test-Path $failedFolderPath)) {
    New-Item -Path $failedFolderPath -ItemType Directory | Out-Null
    Write-Host "📁 Created folder for failed imports: $failedFolderPath" -ForegroundColor Yellow
}

Write-Host "`n🚀 Starting import of $totalFiles MSG files..." -ForegroundColor Cyan
Write-Host "📊 Progress will be shown every $batchSize files." -ForegroundColor Yellow

# ======================
# IMPORT LOOP
# ======================
$startTime = Get-Date

foreach ($file in $msgFiles) {
    $currentFileName = $file.Name

    try {
        $mail = $namespace.OpenSharedItem($file.FullName)
        $subFolderName = Get-FolderFromMetadata $mail

        if (-not $folderStats.ContainsKey($subFolderName)) {
            $folderStats[$subFolderName] = 0
        }
        $folderStats[$subFolderName]++

        $destinationFolder = Get-OrCreate-OutlookFolder -parentFolder $targetRootFolder -subFolderName $subFolderName

        $copiedMail = $mail.Copy()
        $copiedMail.Move($destinationFolder) | Out-Null

        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null

        $successCount++

    } catch {
        Write-Warning "⚠️ Failed to import: $currentFileName - $($_.Exception.Message)"
        $failedList += @{
            File = $file.FullName
            Error = $_.Exception.Message
            Time = Get-Date
        }
        $failedCount++

        try {
            $failedFileName = $file.Name
            $failedDestination = Join-Path $failedFolderPath $failedFileName

            $counter = 1
            while (Test-Path $failedDestination) {
                $nameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($failedFileName)
                $extension = [System.IO.Path]::GetExtension($failedFileName)
                $failedDestination = Join-Path $failedFolderPath "$nameWithoutExt`_$counter$extension"
                $counter++
            }

            Move-Item $file.FullName $failedDestination -Force
            Write-Host "📁 Moved failed file to: $failedDestination" -ForegroundColor Yellow

        } catch {
            Write-Warning "❌ Also failed to move $currentFileName to failed_imported_mail: $($_.Exception.Message)"
        }
    }

    $processedCount++

    # Show progress
    $modResult = $processedCount % 10
    if ($modResult -eq 0 -or $processedCount -eq $totalFiles) {
        $percentComplete = [math]::Round(($processedCount / $totalFiles) * 100, 1)
        $elapsed = (Get-Date) - $startTime
        $estimatedTotal = if ($processedCount -gt 0) { $elapsed.TotalSeconds * ($totalFiles / $processedCount) } else { 0 }
        $remaining = [TimeSpan]::FromSeconds($estimatedTotal - $elapsed.TotalSeconds)

        $progressMsg = "📊 Progress: $processedCount / $totalFiles ($percentComplete percent) - Success: $successCount - Failed: $failedCount - ETA: $($remaining.ToString('hh\:mm\:ss'))"
        Write-Host $progressMsg -ForegroundColor Cyan
    }

    # Batch pause to prevent Outlook from hanging
    $batchModResult = $processedCount % $batchSize
    if ($batchModResult -eq 0 -and $processedCount -lt $totalFiles) {
        Write-Host "⏸️ Pausing for $waitSeconds seconds to prevent Outlook overload..." -ForegroundColor Yellow
        Start-Sleep -Seconds $waitSeconds

        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
    }
}

# ======================
# CLEANUP AND SUMMARY
# ======================
$endTime = Get-Date
$totalDuration = $endTime - $startTime

Write-Host "`n🧹 Cleaning up COM objects..." -ForegroundColor Cyan
try {
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
} catch {
    Write-Warning "⚠️ Error during COM cleanup: $($_.Exception.Message)"
}

Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "📊 IMPORT SUMMARY" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan
Write-Host "📁 Target Folder: $targetFolderName" -ForegroundColor White
Write-Host "📧 Total files found: $totalFiles" -ForegroundColor White
Write-Host "✅ Successfully imported: $successCount" -ForegroundColor Green
Write-Host "❌ Failed to import: $failedCount" -ForegroundColor Red
Write-Host "⏱️ Total duration: $($totalDuration.ToString('hh\:mm\:ss'))" -ForegroundColor White

if ($folderStats.Count -gt 0) {
    Write-Host "`n📂 Files imported by folder:" -ForegroundColor Yellow
    $folderStats.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "   $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

if ($failedCount -gt 0) {
    Write-Host "`n⚠️ FAILED IMPORTS:" -ForegroundColor Red

    $logPath = Join-Path $sourceFolder $logFile
    $logContent = @()
    $logContent += "Failed MSG Import Log - $(Get-Date)"
    $logContent += "="*50
    $logContent += "Target Folder: $targetFolderName"
    $logContent += "Total Files: $totalFiles"
    $logContent += "Failed Count: $failedCount"
    $logContent += ""
    $logContent += "Failed Files:"
    $logContent += "-"*30

    foreach ($failed in $failedList) {
        if ($failed -is [hashtable]) {
            $logContent += "File: $($failed.File)"
            $logContent += "Error: $($failed.Error)"
            $logContent += "Time: $($failed.Time)"
            $logContent += ""
        } else {
            $logContent += "File: $failed"
            $logContent += ""
        }
    }

    $logContent | Out-File -FilePath $logPath -Encoding UTF8
    Write-Host "📄 Detailed failure log saved to: $logPath" -ForegroundColor Yellow
    Write-Host "📁 Failed files moved to: $failedFolderPath" -ForegroundColor Yellow

    Write-Host "`n💡 Troubleshooting tips for failed imports:" -ForegroundColor Cyan
    Write-Host "   • Check if MSG files are corrupted or password-protected" -ForegroundColor White
    Write-Host "   • Ensure sufficient disk space in Outlook data file" -ForegroundColor White
    Write-Host "   • Try running the script with fewer files in smaller batches" -ForegroundColor White
    Write-Host "   • Check Outlook data file (.pst/.ost) for corruption" -ForegroundColor White
}

Write-Host "`n✅ Import process completed!" -ForegroundColor Green
if ($successCount -gt 0) {
    Write-Host "🎉 Check your Outlook folder '$targetFolderName' to see the imported emails organized by folder structure." -ForegroundColor Green
}
