# ======================
# MSG Import Script - Content Preserving Version
# ======================
# This version uses a different method to preserve email content

param(
    [string]$SourceFolder = "",
    [string]$TargetFolder = ""
)

$batchSize = 20
$waitSeconds = 3

Write-Host "=== MSG Import Tool (Content Preserving) ===" -ForegroundColor Cyan
Write-Host ""

# Get source folder
if ([string]::IsNullOrEmpty($SourceFolder)) {
    $SourceFolder = Read-Host "Enter full path to the folder containing .msg files"
}

if (!(Test-Path $SourceFolder)) {
    Write-Error "Invalid folder path: $SourceFolder"
    exit 1
}

# Count MSG files
$msgFiles = Get-ChildItem -Path $SourceFolder -Filter *.msg -Recurse
if ($msgFiles.Count -eq 0) {
    Write-Error "No .msg files found in: $SourceFolder"
    exit 1
}

Write-Host "Found $($msgFiles.Count) MSG files to import." -ForegroundColor Green

# Get target folder
if ([string]::IsNullOrEmpty($TargetFolder)) {
    $TargetFolder = Read-Host "Enter Outlook root folder name (e.g., Testing)"
}

if ([string]::IsNullOrWhiteSpace($TargetFolder)) {
    Write-Error "Target folder name cannot be empty."
    exit 1
}

# Functions
function Get-OrCreateFolder {
    param($parentFolder, $folderName)
    try {
        return $parentFolder.Folders.Item($folderName)
    } catch {
        return $parentFolder.Folders.Add($folderName)
    }
}

function Get-MessageFolder {
    param($mail)
    try {
        $messageClass = $mail.MessageClass
        $isSent = $mail.Sent
        $sentOn = $mail.SentOn
        $receivedTime = $mail.ReceivedTime
        
        if ($messageClass -like "IPM.Note*") {
            if ($isSent -and $null -ne $sentOn -and $sentOn -ne [datetime]::MinValue) {
                return "Sent Items"
            } elseif ($mail.Sent -eq $false -and ($null -eq $receivedTime -or $receivedTime -eq [datetime]::MinValue)) {
                return "Drafts"
            } elseif ($null -ne $receivedTime -and $receivedTime -ne [datetime]::MinValue) {
                return "Inbox"
            }
        } elseif ($messageClass -like "IPM.Schedule*") {
            return "Calendar"
        } elseif ($messageClass -like "IPM.Contact*") {
            return "Contacts"
        } elseif ($messageClass -like "IPM.Task*") {
            return "Tasks"
        }
        
        return "Unclassified"
    } catch {
        return "Unclassified"
    }
}

function Import-MsgFileAlternative {
    param($msgFilePath, $destinationFolder)
    
    try {
        # Method 1: Try direct import using Outlook's ImportFromFile (if available)
        # This method often preserves content better than Copy/Move
        
        # Open the MSG file
        $mail = $namespace.OpenSharedItem($msgFilePath)
        
        # Create a new mail item in the destination folder
        $newMail = $destinationFolder.Items.Add()
        
        # Copy all properties manually to preserve content
        $newMail.Subject = $mail.Subject
        $newMail.Body = $mail.Body
        $newMail.HTMLBody = $mail.HTMLBody
        $newMail.SenderName = $mail.SenderName
        $newMail.SenderEmailAddress = $mail.SenderEmailAddress
        $newMail.To = $mail.To
        $newMail.CC = $mail.CC
        $newMail.BCC = $mail.BCC
        $newMail.SentOn = $mail.SentOn
        $newMail.ReceivedTime = $mail.ReceivedTime
        $newMail.Importance = $mail.Importance
        $newMail.Sensitivity = $mail.Sensitivity
        
        # Copy attachments
        if ($mail.Attachments.Count -gt 0) {
            for ($i = 1; $i -le $mail.Attachments.Count; $i++) {
                $attachment = $mail.Attachments.Item($i)
                $tempFile = [System.IO.Path]::GetTempFileName()
                $attachment.SaveAsFile($tempFile)
                $newMail.Attachments.Add($tempFile) | Out-Null
                Remove-Item $tempFile -Force -ErrorAction SilentlyContinue
            }
        }
        
        # Save the new mail item
        $newMail.Save()
        
        # Clean up
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($newMail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        
        return $true
        
    } catch {
        Write-Verbose "Alternative import failed: $($_.Exception.Message)"
        
        # Clean up on error
        if ($newMail) {
            try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($newMail) | Out-Null } catch { }
        }
        if ($mail) {
            try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null } catch { }
        }
        
        return $false
    }
}

function Import-MsgFileStandard {
    param($msgFilePath, $destinationFolder)
    
    try {
        $mail = $namespace.OpenSharedItem($msgFilePath)
        $copiedMail = $mail.Copy()
        $copiedMail.Move($destinationFolder) | Out-Null
        
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        
        return $true
        
    } catch {
        Write-Verbose "Standard import failed: $($_.Exception.Message)"
        return $false
    }
}

# Initialize Outlook
Write-Host "Initializing Outlook..." -ForegroundColor Cyan

try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook
    $outlook = New-Object -ComObject Outlook.Application
    $namespace = $outlook.GetNamespace("MAPI")
    Write-Host "Outlook connection successful." -ForegroundColor Green
} catch {
    Write-Error "Failed to connect to Outlook: $($_.Exception.Message)"
    exit 1
}

# Setup target folder
try {
    $defaultStore = $namespace.DefaultStore
    $rootFolders = $defaultStore.GetRootFolder().Folders
    
    $targetRootFolder = $null
    foreach ($folder in $rootFolders) {
        if ($folder.Name -eq $TargetFolder) {
            $targetRootFolder = $folder
            break
        }
    }
    
    if (-not $targetRootFolder) {
        $targetRootFolder = $rootFolders.Add($TargetFolder)
        Write-Host "Created root folder: $TargetFolder" -ForegroundColor Green
    } else {
        Write-Host "Using existing folder: $TargetFolder" -ForegroundColor Green
    }
} catch {
    Write-Error "Failed to setup target folder: $($_.Exception.Message)"
    exit 1
}

# Tracking variables
$totalFiles = $msgFiles.Count
$processedCount = 0
$successCount = 0
$failedCount = 0
$folderStats = @{}
$alternativeSuccessCount = 0
$standardSuccessCount = 0

Write-Host ""
Write-Host "Starting import of $totalFiles MSG files..." -ForegroundColor Cyan
Write-Host "Using content-preserving import method..." -ForegroundColor Yellow
$startTime = Get-Date

# Import loop
foreach ($file in $msgFiles) {
    $currentFileName = $file.Name
    $importSuccess = $false
    
    try {
        # First, determine the target subfolder
        $mail = $namespace.OpenSharedItem($file.FullName)
        $subFolderName = Get-MessageFolder $mail
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        
        if (-not $folderStats.ContainsKey($subFolderName)) {
            $folderStats[$subFolderName] = 0
        }
        
        $destinationFolder = Get-OrCreateFolder $targetRootFolder $subFolderName
        
        # Try alternative import method first (better content preservation)
        $importSuccess = Import-MsgFileAlternative $file.FullName $destinationFolder
        
        if ($importSuccess) {
            $alternativeSuccessCount++
            Write-Verbose "Alternative import successful: $currentFileName"
        } else {
            # Fallback to standard method
            $importSuccess = Import-MsgFileStandard $file.FullName $destinationFolder
            
            if ($importSuccess) {
                $standardSuccessCount++
                Write-Verbose "Standard import successful: $currentFileName"
            }
        }
        
        if ($importSuccess) {
            $folderStats[$subFolderName]++
            $successCount++
        } else {
            Write-Warning "Failed to import: $currentFileName"
            $failedCount++
        }
        
    } catch {
        Write-Warning "Error importing $currentFileName`: $($_.Exception.Message)"
        $failedCount++
    }
    
    $processedCount++
    
    # Progress update
    if (($processedCount % 5) -eq 0 -or $processedCount -eq $totalFiles) {
        $percentComplete = [math]::Round(($processedCount / $totalFiles) * 100, 1)
        Write-Host "Progress: $processedCount / $totalFiles ($percentComplete%) - Success: $successCount - Failed: $failedCount" -ForegroundColor Cyan
    }
    
    # Batch cleanup
    if (($processedCount % $batchSize) -eq 0 -and $processedCount -lt $totalFiles) {
        Write-Host "Cleaning up memory..." -ForegroundColor Yellow
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
        Start-Sleep -Seconds $waitSeconds
    }
}

# Cleanup
try {
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
} catch { }

# Summary
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host ""
Write-Host "=== IMPORT SUMMARY ===" -ForegroundColor Cyan
Write-Host "Target Folder: $TargetFolder" -ForegroundColor White
Write-Host "Total files: $totalFiles" -ForegroundColor White
Write-Host "Successfully imported: $successCount" -ForegroundColor Green
Write-Host "  - Alternative method: $alternativeSuccessCount" -ForegroundColor Gray
Write-Host "  - Standard method: $standardSuccessCount" -ForegroundColor Gray
Write-Host "Failed to import: $failedCount" -ForegroundColor Red
Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White

if ($folderStats.Count -gt 0) {
    Write-Host ""
    Write-Host "Files by folder:" -ForegroundColor Yellow
    $folderStats.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Import completed!" -ForegroundColor Green
Write-Host "Check your Outlook '$TargetFolder' folder for the imported emails." -ForegroundColor Green
Write-Host "The alternative method should preserve email content better." -ForegroundColor Yellow
