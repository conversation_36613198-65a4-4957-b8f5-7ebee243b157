# ======================
# MSG File Import Script - Simple Version
# ======================

param(
    [string]$SourceFolder = "",
    [string]$TargetFolder = ""
)

# Configuration
$batchSize = 50
$waitSeconds = 3

Write-Host "=== MSG File Import Tool ===" -ForegroundColor Cyan
Write-Host ""

# Get source folder
if ([string]::IsNullOrEmpty($SourceFolder)) {
    $SourceFolder = Read-Host "Enter full path to the folder containing .msg files"
}

if (!(Test-Path $SourceFolder)) {
    Write-Error "The folder path '$SourceFolder' is invalid. Exiting."
    exit 1
}

# Count MSG files
$msgFiles = Get-ChildItem -Path $SourceFolder -Filter *.msg -Recurse
if ($msgFiles.Count -eq 0) {
    Write-Error "No .msg files found in '$SourceFolder'. Exiting."
    exit 1
}

Write-Host "Found $($msgFiles.Count) MSG files to import." -ForegroundColor Green

# Get target folder name
if ([string]::IsNullOrEmpty($TargetFolder)) {
    $TargetFolder = Read-Host "Enter the Outlook root-level folder name to import into (e.g., Testing)"
}

if ([string]::IsNullOrWhiteSpace($TargetFolder)) {
    Write-Error "Root folder name cannot be empty. Exiting."
    exit 1
}

# Function to get or create folder
function Get-OrCreateFolder {
    param($parentFolder, $folderName)
    try {
        return $parentFolder.Folders.Item($folderName)
    } catch {
        return $parentFolder.Folders.Add($folderName)
    }
}

# Function to determine folder from message
function Get-MessageFolder {
    param($mail)
    try {
        $messageClass = $mail.MessageClass
        $isSent = $mail.Sent
        $sentOn = $mail.SentOn
        $receivedTime = $mail.ReceivedTime
        
        if ($messageClass -like "IPM.Note*") {
            if ($isSent -and $null -ne $sentOn -and $sentOn -ne [datetime]::MinValue) {
                return "Sent Items"
            } elseif ($mail.Sent -eq $false -and ($null -eq $receivedTime -or $receivedTime -eq [datetime]::MinValue)) {
                return "Drafts"
            } elseif ($null -ne $receivedTime -and $receivedTime -ne [datetime]::MinValue) {
                return "Inbox"
            }
        } elseif ($messageClass -like "IPM.Schedule*") {
            return "Calendar"
        } elseif ($messageClass -like "IPM.Contact*") {
            return "Contacts"
        } elseif ($messageClass -like "IPM.Task*") {
            return "Tasks"
        }
        
        return "Unclassified"
    } catch {
        return "Unclassified"
    }
}

# Initialize Outlook
Write-Host "Initializing Outlook connection..." -ForegroundColor Cyan

try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook
    $outlook = New-Object -ComObject Outlook.Application
    $namespace = $outlook.GetNamespace("MAPI")
    Write-Host "Outlook connection successful." -ForegroundColor Green
} catch {
    Write-Error "Failed to connect to Outlook: $($_.Exception.Message)"
    Write-Host "Make sure Classic Outlook is installed and configured." -ForegroundColor Yellow
    exit 1
}

# Setup target folder
try {
    $defaultStore = $namespace.DefaultStore
    $rootFolders = $defaultStore.GetRootFolder().Folders
    
    $targetRootFolder = $null
    foreach ($folder in $rootFolders) {
        if ($folder.Name -eq $TargetFolder) {
            $targetRootFolder = $folder
            break
        }
    }
    
    if (-not $targetRootFolder) {
        $targetRootFolder = $rootFolders.Add($TargetFolder)
        Write-Host "Created Outlook root folder: '$TargetFolder'" -ForegroundColor Green
    } else {
        Write-Host "Using existing Outlook folder: '$TargetFolder'" -ForegroundColor Green
    }
} catch {
    Write-Error "Failed to access or create Outlook folders: $($_.Exception.Message)"
    exit 1
}

# Prepare tracking
$totalFiles = $msgFiles.Count
$processedCount = 0
$successCount = 0
$failedCount = 0
$folderStats = @{}

$failedFolderPath = Join-Path $SourceFolder "failed_imported_mail"
if (!(Test-Path $failedFolderPath)) {
    New-Item -Path $failedFolderPath -ItemType Directory | Out-Null
}

Write-Host ""
Write-Host "Starting import of $totalFiles MSG files..." -ForegroundColor Cyan
$startTime = Get-Date

# Import loop
foreach ($file in $msgFiles) {
    $currentFileName = $file.Name
    
    try {
        $mail = $namespace.OpenSharedItem($file.FullName)
        $subFolderName = Get-MessageFolder $mail
        
        if (-not $folderStats.ContainsKey($subFolderName)) {
            $folderStats[$subFolderName] = 0
        }
        $folderStats[$subFolderName] = $folderStats[$subFolderName] + 1
        
        $destinationFolder = Get-OrCreateFolder $targetRootFolder $subFolderName
        
        $copiedMail = $mail.Copy()
        $copiedMail.Move($destinationFolder) | Out-Null
        
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null
        
        $successCount = $successCount + 1
        
    } catch {
        Write-Warning "Failed to import: $currentFileName - $($_.Exception.Message)"
        $failedCount = $failedCount + 1
        
        try {
            $failedDestination = Join-Path $failedFolderPath $file.Name
            Move-Item $file.FullName $failedDestination -Force
        } catch {
            Write-Warning "Also failed to move $currentFileName to failed folder"
        }
    }

    $processedCount = $processedCount + 1
    
    # Show progress every 10 files
    $remainder = $processedCount - (($processedCount / 10) * 10)
    if ($remainder -eq 0 -or $processedCount -eq $totalFiles) {
        $percentComplete = [math]::Round(($processedCount / $totalFiles) * 100, 1)
        Write-Host "Progress: $processedCount / $totalFiles ($percentComplete percent) - Success: $successCount - Failed: $failedCount" -ForegroundColor Cyan
    }
    
    # Batch pause
    $batchRemainder = $processedCount - (($processedCount / $batchSize) * $batchSize)
    if ($batchRemainder -eq 0 -and $processedCount -lt $totalFiles) {
        Write-Host "Pausing for $waitSeconds seconds..." -ForegroundColor Yellow
        Start-Sleep -Seconds $waitSeconds
    }
}

# Cleanup
try {
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
} catch { }

# Summary
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host ""
Write-Host "=== IMPORT SUMMARY ===" -ForegroundColor Cyan
Write-Host "Target Folder: $TargetFolder" -ForegroundColor White
Write-Host "Total files: $totalFiles" -ForegroundColor White
Write-Host "Successfully imported: $successCount" -ForegroundColor Green
Write-Host "Failed to import: $failedCount" -ForegroundColor Red
Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White

if ($folderStats.Count -gt 0) {
    Write-Host ""
    Write-Host "Files imported by folder:" -ForegroundColor Yellow
    $folderStats.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "  $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Import process completed!" -ForegroundColor Green
if ($successCount -gt 0) {
    Write-Host "Check your Outlook '$TargetFolder' folder to see the imported emails." -ForegroundColor Green
}
