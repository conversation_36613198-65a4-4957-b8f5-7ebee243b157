# ======================
# Outlook Detection Test Script
# ======================
# This script helps diagnose Outlook installation and COM registration issues

Write-Host "=== Outlook Detection and COM Test ===" -ForegroundColor Cyan
Write-Host "This script will help diagnose Outlook installation issues." -ForegroundColor Yellow
Write-Host ""

# ======================
# FUNCTION: Detect All Outlook Installations
# ======================
function Get-AllOutlookInstallations {
    Write-Host "🔍 Scanning for all Outlook installations..." -ForegroundColor Cyan
    
    $installations = @()
    $officeVersions = @("16.0", "15.0", "14.0", "12.0", "11.0")
    $regPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Office",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Office"
    )
    
    foreach ($regPath in $regPaths) {
        foreach ($ver in $officeVersions) {
            $fullPath = Join-Path $regPath "$ver\Outlook\InstallRoot"
            try {
                if (Test-Path $fullPath) {
                    $path = Get-ItemPropertyValue -Path $fullPath -Name "Path" -ErrorAction Stop
                    $exe = Join-Path $path "OUTLOOK.EXE"
                    if (Test-Path $exe) {
                        $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($exe)
                        $installations += @{
                            Path = $exe
                            Version = $version.FileVersion
                            ProductName = $version.ProductName
                            OfficeVersion = $ver
                            RegistryPath = $regPath
                            FileSize = (Get-Item $exe).Length
                            LastModified = (Get-Item $exe).LastWriteTime
                        }
                    }
                }
            } catch {
                # Silently continue
            }
        }
    }
    
    return $installations
}

# ======================
# FUNCTION: Check New Outlook (UWP)
# ======================
function Get-NewOutlookInfo {
    Write-Host "🔍 Checking for New Outlook (UWP)..." -ForegroundColor Cyan
    
    try {
        # Check for New Outlook package
        $newOutlookPackages = Get-AppxPackage -Name "*OutlookForWindows*" -ErrorAction SilentlyContinue
        if ($newOutlookPackages) {
            foreach ($package in $newOutlookPackages) {
                Write-Host "📱 Found New Outlook: $($package.Name) v$($package.Version)" -ForegroundColor Yellow
                Write-Host "   Install Location: $($package.InstallLocation)" -ForegroundColor Gray
            }
            return $true
        }
    } catch {
        # Silently continue
    }
    
    return $false
}

# ======================
# FUNCTION: Test COM Registration
# ======================
function Test-OutlookCOM {
    Write-Host "🔧 Testing Outlook COM registration..." -ForegroundColor Cyan
    
    try {
        Add-Type -AssemblyName Microsoft.Office.Interop.Outlook -ErrorAction Stop
        Write-Host "✅ Outlook Interop Assembly loaded successfully." -ForegroundColor Green
        
        $outlook = New-Object -ComObject Outlook.Application -ErrorAction Stop
        Write-Host "✅ Outlook COM object created successfully." -ForegroundColor Green
        
        $namespace = $outlook.GetNamespace("MAPI")
        Write-Host "✅ MAPI namespace accessed successfully." -ForegroundColor Green
        
        $defaultStore = $namespace.DefaultStore
        if ($defaultStore) {
            Write-Host "✅ Default store found: $($defaultStore.DisplayName)" -ForegroundColor Green
        } else {
            Write-Warning "⚠️ No default store found. Outlook may not be configured."
        }
        
        # Cleanup
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
        
        return $true
        
    } catch {
        Write-Error "❌ COM test failed: $($_.Exception.Message)"
        return $false
    }
}

# ======================
# MAIN EXECUTION
# ======================

# 1. Check all Outlook installations
$installations = Get-AllOutlookInstallations

if ($installations.Count -eq 0) {
    Write-Warning "❌ No Classic Outlook installations found!"
} else {
    Write-Host "✅ Found $($installations.Count) Outlook installation(s):" -ForegroundColor Green
    
    foreach ($install in $installations) {
        Write-Host ""
        Write-Host "📧 Outlook Installation:" -ForegroundColor White
        Write-Host "   Product: $($install.ProductName)" -ForegroundColor Gray
        Write-Host "   Version: $($install.Version)" -ForegroundColor Gray
        Write-Host "   Office Version: $($install.OfficeVersion)" -ForegroundColor Gray
        Write-Host "   Path: $($install.Path)" -ForegroundColor Gray
        Write-Host "   File Size: $([math]::Round($install.FileSize / 1MB, 2)) MB" -ForegroundColor Gray
        Write-Host "   Last Modified: $($install.LastModified)" -ForegroundColor Gray
        Write-Host "   Registry: $($install.RegistryPath)" -ForegroundColor Gray
        
        # Determine if this looks like Classic Outlook
        $isClassic = $install.ProductName -like "*Microsoft Outlook*" -and 
                     $install.ProductName -notlike "*New*" -and
                     $install.FileSize -gt 1MB
        
        if ($isClassic) {
            Write-Host "   Type: Classic Outlook ✅" -ForegroundColor Green
        } else {
            Write-Host "   Type: Unknown/Other ⚠️" -ForegroundColor Yellow
        }
    }
}

Write-Host ""

# 2. Check for New Outlook
$hasNewOutlook = Get-NewOutlookInfo

Write-Host ""

# 3. Test COM registration
$comWorking = Test-OutlookCOM

Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Cyan

if ($installations.Count -eq 0) {
    Write-Host "❌ No Classic Outlook found. Please install Microsoft Office with Outlook." -ForegroundColor Red
} elseif (-not $comWorking) {
    Write-Host "⚠️ Classic Outlook found but COM registration has issues." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 Recommended actions:" -ForegroundColor Cyan
    Write-Host "   1. Run Outlook at least once and complete setup" -ForegroundColor White
    Write-Host "   2. Try running this script as Administrator" -ForegroundColor White
    Write-Host "   3. Re-register Outlook COM manually:" -ForegroundColor White
    
    $newestInstall = $installations | Sort-Object Version -Descending | Select-Object -First 1
    Write-Host "      `"$($newestInstall.Path)`" /regserver" -ForegroundColor Gray
    
} else {
    Write-Host "✅ Classic Outlook is properly installed and COM is working!" -ForegroundColor Green
    Write-Host "🎉 Your MSG import script should work correctly." -ForegroundColor Green
}

if ($hasNewOutlook) {
    Write-Host ""
    Write-Host "ℹ️ Note: New Outlook is also installed. Make sure to use Classic Outlook for MSG imports." -ForegroundColor Blue
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
