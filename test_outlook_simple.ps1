# ======================
# Simple Outlook Detection Test
# ======================

Write-Host "=== Outlook Detection Test ===" -ForegroundColor Cyan
Write-Host ""

# Function to find Outlook installations
function Find-OutlookInstallations {
    Write-Host "Scanning for Outlook installations..." -ForegroundColor Yellow
    
    $installations = @()
    $officeVersions = @("16.0", "15.0", "14.0", "12.0")
    $regPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Office",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Office"
    )
    
    foreach ($regPath in $regPaths) {
        foreach ($ver in $officeVersions) {
            $fullPath = Join-Path $regPath "$ver\Outlook\InstallRoot"
            try {
                if (Test-Path $fullPath) {
                    $path = Get-ItemPropertyValue -Path $fullPath -Name "Path" -ErrorAction Stop
                    $exe = Join-Path $path "OUTLOOK.EXE"
                    if (Test-Path $exe) {
                        $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($exe)
                        $installations += @{
                            Path = $exe
                            Version = $version.FileVersion
                            ProductName = $version.ProductName
                            OfficeVersion = $ver
                        }
                    }
                }
            } catch {
                # Continue silently
            }
        }
    }
    
    return $installations
}

# Function to test COM
function Test-OutlookCOM {
    Write-Host "Testing Outlook COM..." -ForegroundColor Yellow
    
    try {
        Add-Type -AssemblyName Microsoft.Office.Interop.Outlook -ErrorAction Stop
        Write-Host "  Outlook Interop Assembly loaded successfully" -ForegroundColor Green
        
        $outlook = New-Object -ComObject Outlook.Application -ErrorAction Stop
        Write-Host "  Outlook COM object created successfully" -ForegroundColor Green
        
        $namespace = $outlook.GetNamespace("MAPI")
        Write-Host "  MAPI namespace accessed successfully" -ForegroundColor Green
        
        $defaultStore = $namespace.DefaultStore
        if ($defaultStore) {
            Write-Host "  Default store found: $($defaultStore.DisplayName)" -ForegroundColor Green
            $result = $true
        } else {
            Write-Host "  No default store found" -ForegroundColor Yellow
            $result = $false
        }
        
        # Cleanup
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
        
        return $result
        
    } catch {
        Write-Host "  COM test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "1. Checking for Outlook installations..." -ForegroundColor Cyan
$installations = Find-OutlookInstallations

if ($installations.Count -eq 0) {
    Write-Host "  No Outlook installations found!" -ForegroundColor Red
} else {
    Write-Host "  Found $($installations.Count) Outlook installation(s):" -ForegroundColor Green
    
    foreach ($install in $installations) {
        Write-Host ""
        Write-Host "  Product: $($install.ProductName)" -ForegroundColor White
        Write-Host "  Version: $($install.Version)" -ForegroundColor White
        Write-Host "  Office Version: $($install.OfficeVersion)" -ForegroundColor White
        Write-Host "  Path: $($install.Path)" -ForegroundColor White
        
        $isClassic = $install.ProductName -like "*Microsoft Outlook*" -and 
                     $install.ProductName -notlike "*New*"
        
        if ($isClassic) {
            Write-Host "  Type: Classic Outlook" -ForegroundColor Green
        } else {
            Write-Host "  Type: Unknown/Other" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "2. Checking for New Outlook (UWP)..." -ForegroundColor Cyan
try {
    $newOutlookPackages = Get-AppxPackage -Name "*OutlookForWindows*" -ErrorAction SilentlyContinue
    if ($newOutlookPackages) {
        Write-Host "  New Outlook found:" -ForegroundColor Yellow
        foreach ($package in $newOutlookPackages) {
            Write-Host "    $($package.Name) v$($package.Version)" -ForegroundColor White
        }
    } else {
        Write-Host "  No New Outlook found" -ForegroundColor Green
    }
} catch {
    Write-Host "  Could not check for New Outlook" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "3. Testing COM registration..." -ForegroundColor Cyan
$comWorking = Test-OutlookCOM

Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Cyan

if ($installations.Count -eq 0) {
    Write-Host "No Classic Outlook found. Please install Microsoft Office with Outlook." -ForegroundColor Red
} elseif (-not $comWorking) {
    Write-Host "Classic Outlook found but COM registration has issues." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Recommended actions:" -ForegroundColor White
    Write-Host "  1. Run Outlook at least once and complete setup" -ForegroundColor Gray
    Write-Host "  2. Try running this script as Administrator" -ForegroundColor Gray
    Write-Host "  3. Re-register Outlook COM manually:" -ForegroundColor Gray
    
    $newestInstall = $installations | Sort-Object Version -Descending | Select-Object -First 1
    Write-Host "     `"$($newestInstall.Path)`" /regserver" -ForegroundColor Gray
    
} else {
    Write-Host "Classic Outlook is properly installed and COM is working!" -ForegroundColor Green
    Write-Host "Your MSG import script should work correctly." -ForegroundColor Green
}

Write-Host ""
Write-Host "Press Enter to exit..."
Read-Host
