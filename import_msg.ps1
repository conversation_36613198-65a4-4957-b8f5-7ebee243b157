# ======================
# CONFIGURATION
# ======================
$batchSize = 50  # Reduced batch size for better stability
$waitSeconds = 3
$logFile = "failed_imports.txt"

# ======================
# FUNCTION: Detect Classic Outlook Installation
# ======================
function Get-ClassicOutlookPath {
    Write-Host "🔍 Detecting Classic Outlook installation..."

    # Check for Classic Outlook specifically (not New Outlook)
    $officeVersions = @("16.0", "15.0", "14.0", "12.0")
    $regPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Office",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Office"
    )

    $classicOutlookPaths = @()

    foreach ($regPath in $regPaths) {
        foreach ($ver in $officeVersions) {
            $fullPath = Join-Path $regPath "$ver\Outlook\InstallRoot"
            try {
                if (Test-Path $fullPath) {
                    $path = Get-ItemPropertyValue -Path $fullPath -Name "Path" -ErrorAction Stop
                    $exe = Join-Path $path "OUTLOOK.EXE"
                    if (Test-Path $exe) {
                        # Check if this is Classic Outlook by examining file properties
                        $fileInfo = Get-ItemProperty $exe
                        $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($exe)

                        # Classic Outlook typically has "Microsoft Outlook" in product name
                        # and is not the new UWP version
                        if ($version.ProductName -like "*Microsoft Outlook*" -and
                            $version.ProductName -notlike "*New*") {
                            $classicOutlookPaths += @{
                                Path = $exe
                                Version = $version.FileVersion
                                ProductName = $version.ProductName
                            }
                            Write-Host "✅ Found Classic Outlook: $($version.ProductName) v$($version.FileVersion) at $exe"
                        }
                    }
                }
            } catch {
                # Silently continue if registry key doesn't exist
            }
        }
    }

    if ($classicOutlookPaths.Count -eq 0) {
        Write-Warning "❌ No Classic Outlook installation detected."
        Write-Host "💡 Make sure Classic Outlook (not New Outlook) is installed."
        return $null
    }

    # Return the newest version found
    $newestOutlook = $classicOutlookPaths | Sort-Object Version -Descending | Select-Object -First 1
    Write-Host "🎯 Using Classic Outlook: $($newestOutlook.ProductName) at $($newestOutlook.Path)"
    return $newestOutlook.Path
}

# ======================
# FUNCTION: Register Outlook COM if needed
# ======================
function Register-OutlookCom {
    $outlookPath = Get-ClassicOutlookPath

    if (-not $outlookPath) {
        return $false
    }

    Write-Host "🔄 Attempting to re-register COM using: `"$outlookPath`" /regserver"

    try {
        Start-Process -FilePath $outlookPath -ArgumentList "/regserver" -Wait -NoNewWindow -WindowStyle Hidden
        Write-Host "✅ Successfully re-registered Outlook COM."
        Start-Sleep -Seconds 2  # Give COM time to register
        return $true
    } catch {
        Write-Error "❌ Failed to re-register COM: $($_.Exception.Message)"
        return $false
    }
}

# ======================
# FUNCTION: Get or Create Folder
# ======================
function Get-OrCreate-OutlookFolder {
    param (
        [Microsoft.Office.Interop.Outlook.MAPIFolder]$parentFolder,
        [string]$subFolderName
    )
    try {
        return $parentFolder.Folders.Item($subFolderName)
    } catch {
        return $parentFolder.Folders.Add($subFolderName)
    }
}

# ======================
# FUNCTION: Extract Folder Path from MSG Properties
# ======================
function Get-FolderFromMetadata {
    param ($mail)

    try {
        # Try to get the original folder path from various MSG properties
        $folderPath = $null

        # Method 1: Check for PR_PARENT_ENTRYID or folder-related properties
        try {
            # Some MSG files contain folder information in custom properties
            $folderInfo = $mail.PropertyAccessor.GetProperty("http://schemas.microsoft.com/mapi/proptag/0x0E090102")
            if ($folderInfo) {
                Write-Verbose "Found folder info in PR_PARENT_ENTRYID"
            }
        } catch { }

        # Method 2: Check message class and other indicators
        $messageClass = $mail.MessageClass
        $isRead = $mail.UnRead -eq $false
        $isSent = $mail.Sent
        $sentOn = $mail.SentOn
        $receivedTime = $mail.ReceivedTime

        # Method 3: Check for custom folder properties that might be preserved
        try {
            # Try to get folder name from various possible properties
            $possibleFolderProps = @(
                "http://schemas.microsoft.com/mapi/string/{00020329-0000-0000-C000-000000000046}/FolderName",
                "http://schemas.microsoft.com/mapi/string/{00020329-0000-0000-C000-000000000046}/OriginalFolder"
            )

            foreach ($prop in $possibleFolderProps) {
                try {
                    $folderName = $mail.PropertyAccessor.GetProperty($prop)
                    if ($folderName -and $folderName.Trim() -ne "") {
                        return $folderName.Trim()
                    }
                } catch { }
            }
        } catch { }

        # Method 4: Determine folder based on message characteristics
        if ($messageClass -like "IPM.Note*") {
            # Regular email message
            if ($isSent -and $sentOn -ne $null -and $sentOn -ne [datetime]::MinValue) {
                return "Sent Items"
            } elseif ($mail.Sent -eq $false -and ($receivedTime -eq $null -or $receivedTime -eq [datetime]::MinValue)) {
                return "Drafts"
            } elseif ($receivedTime -ne $null -and $receivedTime -ne [datetime]::MinValue) {
                return "Inbox"
            }
        } elseif ($messageClass -like "IPM.Schedule*") {
            return "Calendar"
        } elseif ($messageClass -like "IPM.Contact*") {
            return "Contacts"
        } elseif ($messageClass -like "IPM.Task*") {
            return "Tasks"
        } elseif ($messageClass -like "IPM.Note.Rules*") {
            return "Deleted Items"
        } elseif ($messageClass -like "IPM.Note.SMIME*") {
            # Encrypted/signed messages
            if ($isSent) {
                return "Sent Items"
            } else {
                return "Inbox"
            }
        }

        # Method 5: Check subject line for clues
        $subject = $mail.Subject
        if ($subject) {
            if ($subject -like "*[SPAM]*" -or $subject -like "*[JUNK]*") {
                return "Junk Email"
            }
        }

        # Default to Unclassified if we can't determine the folder
        return "Unclassified"

    } catch {
        Write-Verbose "Error determining folder for message: $($_.Exception.Message)"
        return "Unclassified"
    }
}

# ======================
# FUNCTION: Test Outlook COM Connection
# ======================
function Test-OutlookConnection {
    try {
        Write-Host "🔄 Testing Outlook COM connection..."

        # First, ensure Classic Outlook is detected
        $outlookPath = Get-ClassicOutlookPath
        if (-not $outlookPath) {
            return $false
        }

        # Try to create Outlook COM object
        $outlook = New-Object -ComObject Outlook.Application -ErrorAction Stop
        $namespace = $outlook.GetNamespace("MAPI")

        # Test if we can access the default store
        $defaultStore = $namespace.DefaultStore
        if ($defaultStore) {
            Write-Host "✅ Outlook COM connection successful. Default store: $($defaultStore.DisplayName)"
            return @{
                Success = $true
                Outlook = $outlook
                Namespace = $namespace
            }
        } else {
            Write-Warning "⚠️ Outlook COM connected but no default store found."
            return $false
        }
    } catch {
        Write-Warning "❌ Outlook COM connection failed: $($_.Exception.Message)"
        return $false
    }
}

# ======================
# INPUT: Source Folder
# ======================
Write-Host "=== MSG File Import Tool ===" -ForegroundColor Cyan
Write-Host "This tool will import MSG files into Outlook with folder structure reconstruction." -ForegroundColor Yellow
Write-Host ""

$sourceFolder = Read-Host "Enter full path to the folder containing .msg files"
if (!(Test-Path $sourceFolder)) {
    Write-Error "❌ The folder path '$sourceFolder' is invalid. Exiting."
    exit
}

# Count MSG files
$msgFiles = Get-ChildItem -Path $sourceFolder -Filter *.msg -Recurse
if ($msgFiles.Count -eq 0) {
    Write-Error "❌ No .msg files found in '$sourceFolder'. Exiting."
    exit
}

Write-Host "📧 Found $($msgFiles.Count) MSG files to import." -ForegroundColor Green

# ======================
# INPUT: Outlook Root Folder
# ======================
$targetFolderName = Read-Host "Enter the Outlook root-level folder name to import into (e.g., 'Testing')"
if ([string]::IsNullOrWhiteSpace($targetFolderName)) {
    Write-Error "❌ Root folder name cannot be empty. Exiting."
    exit
}

# ======================
# Ensure Outlook COM works
# ======================
Write-Host "`n🔧 Initializing Outlook connection..." -ForegroundColor Cyan

# Load Outlook Interop Assembly
try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook -ErrorAction Stop
    Write-Host "✅ Outlook Interop Assembly loaded."
} catch {
    Write-Error "❌ Failed to load Outlook Interop Assembly. Please ensure Office is properly installed."
    exit
}

# Test Outlook connection
$connectionResult = Test-OutlookConnection
if ($connectionResult -eq $false) {
    Write-Warning "❌ Initial Outlook COM connection failed. Attempting to re-register..."

    if (-not (Register-OutlookCom)) {
        Write-Error "❌ Unable to register Outlook COM. Please ensure:"
        Write-Host "   1. Classic Outlook (not New Outlook) is installed" -ForegroundColor Yellow
        Write-Host "   2. Outlook has been run at least once and configured" -ForegroundColor Yellow
        Write-Host "   3. Run this script as Administrator if needed" -ForegroundColor Yellow
        exit
    }

    # Wait a moment and try again
    Start-Sleep -Seconds 3
    $connectionResult = Test-OutlookConnection

    if ($connectionResult -eq $false) {
        Write-Error "❌ Still cannot initialize Outlook COM after re-registration. Exiting."
        exit
    }
}

$outlook = $connectionResult.Outlook
$namespace = $connectionResult.Namespace

# ======================
# INIT OUTLOOK FOLDERS
# ======================
Write-Host "`n📁 Setting up Outlook folder structure..." -ForegroundColor Cyan

try {
    # Get the default mailbox (first folder in namespace)
    $defaultStore = $namespace.DefaultStore
    $rootFolders = $defaultStore.GetRootFolder().Folders

    # Try to find existing target folder
    $targetRootFolder = $null
    try {
        foreach ($folder in $rootFolders) {
            if ($folder.Name -eq $targetFolderName) {
                $targetRootFolder = $folder
                break
            }
        }
    } catch { }

    # Create target folder if it doesn't exist
    if (-not $targetRootFolder) {
        $targetRootFolder = $rootFolders.Add($targetFolderName)
        Write-Host "✅ Created Outlook root folder: '$targetFolderName'" -ForegroundColor Green
    } else {
        Write-Host "✅ Using existing Outlook folder: '$targetFolderName'" -ForegroundColor Green
    }

} catch {
    Write-Error "❌ Failed to access or create Outlook folders: $($_.Exception.Message)"
    exit
}

# ======================
# PREPARE IMPORT TRACKING
# ======================
$totalFiles = $msgFiles.Count
$processedCount = 0
$failedCount = 0
$successCount = 0
$failedList = @()
$folderStats = @{}

# Create failed imports folder
$failedFolderPath = Join-Path $sourceFolder "failed_imported_mail"
if (!(Test-Path $failedFolderPath)) {
    New-Item -Path $failedFolderPath -ItemType Directory | Out-Null
    Write-Host "📁 Created folder for failed imports: $failedFolderPath" -ForegroundColor Yellow
}

Write-Host "`n🚀 Starting import of $totalFiles MSG files..." -ForegroundColor Cyan
Write-Host "📊 Progress will be shown every $batchSize files." -ForegroundColor Yellow

# ======================
# IMPORT LOOP
# ======================
$startTime = Get-Date

foreach ($file in $msgFiles) {
    $currentFileName = $file.Name

    try {
        # Open the MSG file
        $mail = $namespace.OpenSharedItem($file.FullName)

        # Determine the destination folder based on metadata
        $subFolderName = Get-FolderFromMetadata $mail

        # Track folder statistics
        if (-not $folderStats.ContainsKey($subFolderName)) {
            $folderStats[$subFolderName] = 0
        }
        $folderStats[$subFolderName]++

        # Get or create the destination folder
        $destinationFolder = Get-OrCreate-OutlookFolder -parentFolder $targetRootFolder -subFolderName $subFolderName

        # Copy the mail item to the destination folder
        $copiedMail = $mail.Copy()
        $copiedMail.Move($destinationFolder) | Out-Null

        # Close the original mail object to free resources
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null

        $successCount++

    } catch {
        Write-Warning "⚠️ Failed to import: $currentFileName - $($_.Exception.Message)"
        $failedList += @{
            File = $file.FullName
            Error = $_.Exception.Message
            Time = Get-Date
        }
        $failedCount++

        # Move failed file to failed folder
        try {
            $failedFileName = $file.Name
            $failedDestination = Join-Path $failedFolderPath $failedFileName

            # Handle duplicate names in failed folder
            $counter = 1
            while (Test-Path $failedDestination) {
                $nameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($failedFileName)
                $extension = [System.IO.Path]::GetExtension($failedFileName)
                $failedDestination = Join-Path $failedFolderPath "$nameWithoutExt`_$counter$extension"
                $counter++
            }

            Move-Item $file.FullName $failedDestination -Force
            Write-Host "📁 Moved failed file to: $failedDestination" -ForegroundColor Yellow

        } catch {
            Write-Warning "❌ Also failed to move $currentFileName to failed_imported_mail: $($_.Exception.Message)"
        }
    }

    $processedCount++

    # Show progress
    if (($processedCount % 10) -eq 0 -or $processedCount -eq $totalFiles) {
        $percentComplete = [math]::Round(($processedCount / $totalFiles) * 100, 1)
        $elapsed = (Get-Date) - $startTime
        $estimatedTotal = if ($processedCount -gt 0) { $elapsed.TotalSeconds * ($totalFiles / $processedCount) } else { 0 }
        $remaining = [TimeSpan]::FromSeconds($estimatedTotal - $elapsed.TotalSeconds)

        Write-Host "📊 Progress: $processedCount / $totalFiles ($percentComplete%) | Success: $successCount | Failed: $failedCount | ETA: $($remaining.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
    }

    # Batch pause to prevent Outlook from hanging
    if (($processedCount % $batchSize) -eq 0 -and $processedCount -lt $totalFiles) {
        Write-Host "⏸️ Pausing for $waitSeconds seconds to prevent Outlook overload..." -ForegroundColor Yellow
        Start-Sleep -Seconds $waitSeconds

        # Force garbage collection to free up memory
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
    }
}

# ======================
# CLEANUP AND SUMMARY
# ======================
$endTime = Get-Date
$totalDuration = $endTime - $startTime

Write-Host "`n🧹 Cleaning up COM objects..." -ForegroundColor Cyan
try {
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
} catch {
    Write-Warning "⚠️ Error during COM cleanup: $($_.Exception.Message)"
}

Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "📊 IMPORT SUMMARY" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan
Write-Host "📁 Target Folder: $targetFolderName" -ForegroundColor White
Write-Host "📧 Total files found: $totalFiles" -ForegroundColor White
Write-Host "✅ Successfully imported: $successCount" -ForegroundColor Green
Write-Host "❌ Failed to import: $failedCount" -ForegroundColor Red
Write-Host "⏱️ Total duration: $($totalDuration.ToString('hh\:mm\:ss'))" -ForegroundColor White

if ($folderStats.Count -gt 0) {
    Write-Host "`n📂 Files imported by folder:" -ForegroundColor Yellow
    $folderStats.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "   $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

if ($failedCount -gt 0) {
    Write-Host "`n⚠️ FAILED IMPORTS:" -ForegroundColor Red

    # Create detailed failed imports log
    $logPath = Join-Path $sourceFolder $logFile
    $logContent = @()
    $logContent += "Failed MSG Import Log - $(Get-Date)"
    $logContent += "="*50
    $logContent += "Target Folder: $targetFolderName"
    $logContent += "Total Files: $totalFiles"
    $logContent += "Failed Count: $failedCount"
    $logContent += ""
    $logContent += "Failed Files:"
    $logContent += "-"*30

    foreach ($failed in $failedList) {
        if ($failed -is [hashtable]) {
            $logContent += "File: $($failed.File)"
            $logContent += "Error: $($failed.Error)"
            $logContent += "Time: $($failed.Time)"
            $logContent += ""
        } else {
            $logContent += "File: $failed"
            $logContent += ""
        }
    }

    $logContent | Out-File -FilePath $logPath -Encoding UTF8
    Write-Host "📄 Detailed failure log saved to: $logPath" -ForegroundColor Yellow
    Write-Host "📁 Failed files moved to: $failedFolderPath" -ForegroundColor Yellow

    Write-Host "`n💡 Troubleshooting tips for failed imports:" -ForegroundColor Cyan
    Write-Host "   • Check if MSG files are corrupted or password-protected" -ForegroundColor White
    Write-Host "   • Ensure sufficient disk space in Outlook data file" -ForegroundColor White
    Write-Host "   • Try running the script with fewer files in smaller batches" -ForegroundColor White
    Write-Host "   • Check Outlook data file (.pst/.ost) for corruption" -ForegroundColor White
}

Write-Host "`n✅ Import process completed!" -ForegroundColor Green
if ($successCount -gt 0) {
    Write-Host "🎉 Check your Outlook '$targetFolderName' folder to see the imported emails organized by folder structure." -ForegroundColor Green
}
