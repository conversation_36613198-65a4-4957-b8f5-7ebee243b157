# Simple MSG Content Test
param([string]$TestMsgFile = "")

Write-Host "=== MSG Content Test ===" -ForegroundColor Cyan

if ([string]::IsNullOrEmpty($TestMsgFile)) {
    $TestMsgFile = Read-Host "Enter path to a .msg file to test"
}

if (!(Test-Path $TestMsgFile)) {
    Write-Error "File not found: $TestMsgFile"
    exit 1
}

try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook
    $outlook = New-Object -ComObject Outlook.Application
    $namespace = $outlook.GetNamespace("MAPI")
    
    Write-Host "Opening MSG file..." -ForegroundColor Yellow
    $mail = $namespace.OpenSharedItem($TestMsgFile)
    
    Write-Host ""
    Write-Host "=== ORIGINAL MSG PROPERTIES ===" -ForegroundColor Green
    Write-Host "Subject: $($mail.Subject)"
    Write-Host "From: $($mail.SenderName)"
    Write-Host "Body Length: $($mail.Body.Length) characters"
    Write-Host "HTML Body Length: $($mail.HTMLBody.Length) characters"
    Write-Host "Size: $($mail.Size) bytes"
    
    if ($mail.Body.Length -gt 0) {
        $preview = $mail.Body.Substring(0, [Math]::Min(100, $mail.Body.Length))
        Write-Host "Body Preview: $preview..." -ForegroundColor Gray
    } else {
        Write-Host "NO BODY CONTENT FOUND!" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "=== TESTING COPY ===" -ForegroundColor Yellow
    $copiedMail = $mail.Copy()
    
    Write-Host "Copied Subject: $($copiedMail.Subject)"
    Write-Host "Copied Body Length: $($copiedMail.Body.Length) characters"
    Write-Host "Copied HTML Body Length: $($copiedMail.HTMLBody.Length) characters"
    Write-Host "Copied Size: $($copiedMail.Size) bytes"
    
    if ($copiedMail.Body.Length -ne $mail.Body.Length) {
        Write-Host "PROBLEM: Copy changed body length!" -ForegroundColor Red
    } else {
        Write-Host "Copy preserved content correctly." -ForegroundColor Green
    }
    
    # Cleanup
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    
} catch {
    Write-Error "Error: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "Test completed. Press Enter to exit..."
Read-Host
