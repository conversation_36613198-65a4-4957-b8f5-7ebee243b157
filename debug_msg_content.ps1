# ======================
# MSG Content Debug Script
# ======================
# This script will help us understand what's happening with MSG content

param(
    [string]$TestMsgFile = ""
)

Write-Host "=== MSG Content Debug Tool ===" -ForegroundColor Cyan
Write-Host ""

# Get a test MSG file
if ([string]::IsNullOrEmpty($TestMsgFile)) {
    $TestMsgFile = Read-Host "Enter full path to a single .msg file to test"
}

if (!(Test-Path $TestMsgFile)) {
    Write-Error "The file '$TestMsgFile' does not exist."
    exit 1
}

Write-Host "Testing MSG file: $TestMsgFile" -ForegroundColor Yellow
Write-Host ""

# Initialize Outlook
try {
    Add-Type -AssemblyName Microsoft.Office.Interop.Outlook
    $outlook = New-Object -ComObject Outlook.Application
    $namespace = $outlook.GetNamespace("MAPI")
    Write-Host "✅ Outlook connection successful." -ForegroundColor Green
} catch {
    Write-Error "❌ Failed to connect to Outlook: $($_.Exception.Message)"
    exit 1
}

try {
    Write-Host "📧 Opening MSG file..." -ForegroundColor Cyan
    $mail = $namespace.OpenSharedItem($TestMsgFile)
    
    Write-Host ""
    Write-Host "=== MSG FILE PROPERTIES ===" -ForegroundColor Yellow
    Write-Host "Subject: '$($mail.Subject)'" -ForegroundColor White
    Write-Host "From: '$($mail.SenderName)' <$($mail.SenderEmailAddress)>" -ForegroundColor White
    Write-Host "To: '$($mail.To)'" -ForegroundColor White
    Write-Host "CC: '$($mail.CC)'" -ForegroundColor White
    Write-Host "BCC: '$($mail.BCC)'" -ForegroundColor White
    Write-Host "Sent: $($mail.SentOn)" -ForegroundColor White
    Write-Host "Received: $($mail.ReceivedTime)" -ForegroundColor White
    Write-Host "Message Class: '$($mail.MessageClass)'" -ForegroundColor White
    Write-Host "Size: $($mail.Size) bytes" -ForegroundColor White
    Write-Host "Has Attachments: $($mail.Attachments.Count -gt 0)" -ForegroundColor White
    Write-Host "Is Sent: $($mail.Sent)" -ForegroundColor White
    Write-Host "Is Read: $(-not $mail.UnRead)" -ForegroundColor White
    
    Write-Host ""
    Write-Host "=== BODY CONTENT ===" -ForegroundColor Yellow
    
    # Check different body formats
    $bodyText = $mail.Body
    $bodyHTML = $mail.HTMLBody
    $bodyRTF = $mail.RTFBody
    
    Write-Host "Body (Text) Length: $($bodyText.Length) characters" -ForegroundColor White
    Write-Host "Body (HTML) Length: $($bodyHTML.Length) characters" -ForegroundColor White
    Write-Host "Body (RTF) Length: $($bodyRTF.Length) characters" -ForegroundColor White
    
    if ($bodyText.Length -gt 0) {
        Write-Host ""
        Write-Host "--- TEXT BODY (First 200 chars) ---" -ForegroundColor Green
        $preview = if ($bodyText.Length -gt 200) { $bodyText.Substring(0, 200) + "..." } else { $bodyText }
        Write-Host $preview -ForegroundColor Gray
    } else {
        Write-Host "❌ No text body content found!" -ForegroundColor Red
    }
    
    if ($bodyHTML.Length -gt 0) {
        Write-Host ""
        Write-Host "--- HTML BODY (First 200 chars) ---" -ForegroundColor Green
        $preview = if ($bodyHTML.Length -gt 200) { $bodyHTML.Substring(0, 200) + "..." } else { $bodyHTML }
        Write-Host $preview -ForegroundColor Gray
    } else {
        Write-Host "❌ No HTML body content found!" -ForegroundColor Red
    }
    
    # Check attachments
    if ($mail.Attachments.Count -gt 0) {
        Write-Host ""
        Write-Host "=== ATTACHMENTS ===" -ForegroundColor Yellow
        for ($i = 1; $i -le $mail.Attachments.Count; $i++) {
            $attachment = $mail.Attachments.Item($i)
            Write-Host "  $i. $($attachment.FileName) ($($attachment.Size) bytes)" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "=== TESTING COPY OPERATION ===" -ForegroundColor Cyan
    
    # Test the copy operation
    Write-Host "Creating a copy of the mail item..." -ForegroundColor Yellow
    $copiedMail = $mail.Copy()
    
    Write-Host "Copied mail properties:" -ForegroundColor White
    Write-Host "  Subject: '$($copiedMail.Subject)'" -ForegroundColor Gray
    Write-Host "  Body Length: $($copiedMail.Body.Length)" -ForegroundColor Gray
    Write-Host "  HTML Body Length: $($copiedMail.HTMLBody.Length)" -ForegroundColor Gray
    Write-Host "  Size: $($copiedMail.Size) bytes" -ForegroundColor Gray
    
    if ($copiedMail.Body.Length -eq 0 -and $mail.Body.Length -gt 0) {
        Write-Host "❌ PROBLEM: Copy operation lost the body content!" -ForegroundColor Red
    } elseif ($copiedMail.Body.Length -eq $mail.Body.Length) {
        Write-Host "✅ Copy operation preserved body content correctly." -ForegroundColor Green
    } else {
        Write-Host "⚠️ Copy operation changed body content length." -ForegroundColor Yellow
    }
    
    # Test alternative import method
    Write-Host ""
    Write-Host "=== TESTING ALTERNATIVE IMPORT METHOD ===" -ForegroundColor Cyan
    
    try {
        # Method 1: Direct import without copy
        Write-Host "Testing direct import (without copy)..." -ForegroundColor Yellow
        
        # Get default inbox for testing
        $inbox = $namespace.GetDefaultFolder(6) # olFolderInbox = 6
        
        # Try importing directly
        $importedMail = $namespace.OpenSharedItem($TestMsgFile)
        Write-Host "  Direct import - Body Length: $($importedMail.Body.Length)" -ForegroundColor Gray
        
        # Clean up test objects
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($importedMail) | Out-Null
        
    } catch {
        Write-Host "❌ Alternative method failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Clean up
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($copiedMail) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
    
} catch {
    Write-Error "❌ Error analyzing MSG file: $($_.Exception.Message)"
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
} finally {
    # Cleanup
    try {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    } catch { }
}

Write-Host ""
Write-Host "=== RECOMMENDATIONS ===" -ForegroundColor Cyan

Write-Host "1. If the original MSG has content but copy doesn't:" -ForegroundColor White
Write-Host "   - We need to use a different import method" -ForegroundColor Gray

Write-Host "2. If the original MSG has no content:" -ForegroundColor White
Write-Host "   - The MSG file itself may be corrupted or incomplete" -ForegroundColor Gray

Write-Host "3. If copy preserves content but Outlook shows empty:" -ForegroundColor White
Write-Host "   - There may be an issue with the folder move operation" -ForegroundColor Gray

Write-Host ""
Write-Host "Press Enter to exit..."
Read-Host
